import os

import pika

import Singleton


class RabbitConfig(Singleton):
    host: str = "localhost"
    username: str = "trove"
    password: str = "trove"
    port: int = 5672
    enabled: bool = False

    # Singleton pattern, ignoring init
    def __init__(self):
        pass

    def init(self):
        self.populate_from_env()

    def populate_from_env(self):
        if os.getenv("RABBIT_HOST"):
            self.host = os.getenv("RABBIT_HOST")

        if os.getenv("RABBIT_PORT"):
            self.port = int(os.getenv("RABBIT_PORT"))

        if os.getenv("RABBIT_USERNAME"):
            self.username = os.getenv("RABBIT_USERNAME")

        if os.getenv("RABBIT_PASSWORD"):
            self.password = os.getenv("RABBIT_PASSWORD")

    def __str__(self):
        return "{username}:{password}@{host}:{port} ({enabled})".format(
            username=self.username,
            password=self.password,
            host=self.host,
            port=self.port,
            enabled=self.enabled,
        )

    def get_rabbit_connection_params(self):
        credentials = pika.PlainCredentials(self.username, self.password)
        parameters = pika.ConnectionParameters(
            host=self.host, port=self.port, credentials=credentials
        )
        return parameters
