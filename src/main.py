import json
import logging
import os
import sys
from datetime import datetime, timezone

import pandas as pd
import pyodbc
import requests
from bson import ObjectId
from ETRReader.Reader import ETRReader, get_database_connection_info_from_environment
from ETRWriter.ETR import (
    ActiveOutageRecord,
    HistoricalOutageRecord,
    delete_2_week_unlocked_outages,
)
from ETRWriter.Writer import (
    ActiveOutagesDocumentBuilder,
    ETRSessionBuilder,
    HistoricalOutagesDocumentBuilder,
)
from pymongo import MongoClient
from rabbitmq_writer.rabbitmq_client import RabbitMQClient

# --------------------
# Constants
# --------------------
APP_NAME = "STORM-ETL"
UPLOAD_CONFIGS = os.getenv("UPLOAD_CONFIGS", "false").lower() == "true"

# rabbitmq constants
RABBITMQ_EXCHANGE = os.getenv("RABBITMQ_EXCHANGE")
RABBITMQ_ROUTING_KEY = os.getenv("RABBITMQ_ROUTING_KEY")

# Databricks
DATABRICKS = "Databricks"
DBS_AGG_SCHEMA = "active_outage"

# OUTAGE_TABLE = ""
VIEW_TABLE = "view_table"

# --------------------
# ENVIRONMENT
# --------------------

# Mongo Creds
mongo_username = os.getenv("MONGO_USER")
mongo_password = os.getenv("MONGO_PASSWORD")
mongo_host = os.getenv("MONGO_HOST")
mongo_port = int(os.getenv("MONGO_PORT"))

# Logging Setup
logging.basicConfig(format="%(asctime)s %(levelname)s [%(name)s] %(message)s")
logging.getLogger().setLevel(os.environ.get("LOG_LEVEL", logging.INFO))

# Auth Control Flow
ACF_TENANT_ID = os.getenv("ACF_TENANT_ID")  # ap-ramp-XX-svcpri-etl.tenant
ACF_CLIENT_ID = os.getenv("ACF_CLIENT_ID")  # ap-ramp-XX-svcpri-etl.appId
ACF_CLIENT_SECRET = os.getenv("ACF_CLIENT_SECRET")  # ap-ramp-XX-svcpri-etl.password
ACF_SCOPE = os.getenv("ACF_SCOPE")  # DBX Service Scope

# Databricks
DBS_HOST = os.getenv("DBS_HOST")
DBS_PORT = os.getenv("DBS_PORT")
DBS_HTTP_PATH = os.getenv("DBS_HTTP_PATH")

# Queries
DBS_CATALOG = os.getenv("DBS_CATALOG")
DBS_SCHEMA = os.getenv("DBS_SCHEMA", "apc_proj_storm")

# --------------------
# Classes
# --------------------
class TokenRetrievalFailure(Exception):
    pass


activeOutageId = ObjectId()
unaggregatedOutageId = ObjectId()
current_time = datetime.utcnow()
formatted_current_time = current_time.strftime("%Y-%m-%dT%H:%M:%S.%f+00:00")


def to_rfc3339_string(dt):
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)

    return dt.isoformat()


# --------------------
# Functions
# --------------------
def retrieve_access_token() -> str:
    url = f"https://login.microsoftonline.com/{ACF_TENANT_ID}/oauth2/v2.0/token"
    data = {
        "client_id": ACF_CLIENT_ID,
        "client_secret": ACF_CLIENT_SECRET,
        "grant_type": "client_credentials",
        "scope": ACF_SCOPE,
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    try:
        response = requests.post(url, data=data, headers=headers)
        response.raise_for_status()  # Raises an HTTPError if the response status code is not in the 2xx range

        content = response.json()
        return content["access_token"]
    except requests.exceptions.RequestException as e:
        logging.error(f"Network error occurred: {e}")
        sys.exit(1)
    except (TokenRetrievalFailure, ValueError, KeyError) as e:
        logging.error(f"Error retrieving access token: {e}")
        sys.exit(1)


def dbs_conn_string(access_token: str) -> str:
    return (
        f"host={DBS_HOST};"
        + f"port={DBS_PORT};"
        + f"httppath={DBS_HTTP_PATH};"
        + f"auth_accesstoken={access_token};"
        + "thrifttransport=2;"
        + "ssl=1;"
        + "authmech=11;"
        + "auth_flow=0;"
        + "driver=/opt/simba/spark/lib/64/libsparkodbc_sb64.so;"
    )


def df_size(df: pd.DataFrame):
    return df.shape[0]


def run_dbs_query(cursor, query: str):
    df = pd.DataFrame()

    try:
        logging.info(f"Running [{DATABRICKS}] query: '{query}'...")
        cursor.execute(query)
        df = pd.DataFrame.from_records(
            cursor.fetchall(), columns=[col[0] for col in cursor.description]
        )

        if not df.empty:
            logging.info(f"Query returned {df_size(df)} record(s).")
        else:
            logging.warn("Query returned no results.")

    except Exception as e:
        logging.error(e)

    return df


def convert_to_titlecase(value):
    if value is None:
        return ""

    value = value.replace("PD - ", "")

    titlecase_value = " ".join(word.capitalize() for word in value.split())

    return titlecase_value


def _get_column_name(df: pd.DataFrame, potential_names: list[str]) -> str:
    for name in potential_names:
        if name in df.columns:
            return name
    raise KeyError(f"DataFrame is missing a required column. Searched for: {potential_names}")

def process_historical_outages(df: pd.DataFrame):


    def set_locked_value(timestamp) -> bool:
        database_info = get_database_connection_info_from_environment()
        reader = ETRReader(
            database_info["host"], database_info["port"],
            database_info["username"], database_info["password"],
            dbname="etr",
        )
        for timeframe in reader.get_past_two_week_storm_timeframes():
            start, end = timeframe.get("stormStartDate"), timeframe.get("stormEndDate")
            if start and end and start <= timestamp <= end:
                return True
            if start and not end and timestamp >= start:
                return True
        return False

    region_col = _get_column_name(df, ['parent_zone_derived', 'pd_area_name'])
    last_updated_col = _get_column_name(df, ['_md_loaded_tstp_utc', 'last_update'])
    customer_count_col = _get_column_name(df, ['affected_customers_count', 'current_customer'])
    incident_name_col = _get_column_name(df, ['incident_name', 'event_idx'])

    df['processed_region'] = df[region_col].apply(convert_to_titlecase)
    # Convert incident names, handling floats by converting to int first to remove decimals
    def process_incident_name(value):
        if pd.isna(value):
            return "null"
        if isinstance(value, float) and value.is_integer():
            return str(int(value))
        return str(value)

    df['processed_incident_name'] = df[incident_name_col].apply(process_incident_name)
    df['formatted_start_time'] = df['start_time'].dt.strftime("%Y-%m-%dT%H:%M:%S.%f+00:00")

    logging.info("Creating historical outage records...")
    historical_outages = [
        HistoricalOutageRecord(
            region=row.processed_region,
            currentCustomers=int(getattr(row, customer_count_col)),
            affectedCustomers=int(row.affected_customers_count),
            startTime=row.formatted_start_time,
            incidentName=row.processed_incident_name,
        )
        for row in df.itertuples()
    ]

    last_updated = df.iloc[0][last_updated_col]
    data_entry_date = df.iloc[0].data_entry_date
    formatted_data_entry_date = data_entry_date.strftime("%Y-%m-%dT%H:%M:%S.%f+00:00")
    
    logging.info("Creating HistoricalOutagesDocument and writing to Mongo...")
    session = ETRSessionBuilder(sessionType="historical_outage").build()
    with session as s:
        s.add_document(
            HistoricalOutagesDocumentBuilder(
                id=unaggregatedOutageId,
                session=s.id,
                outageRecords=historical_outages,
                lastUpdated=last_updated,
                createdTimestamp=formatted_current_time,
                generatedBy="System",
                dataEntryDate=formatted_data_entry_date,
                locked=set_locked_value(current_time),
            ).build()
        )
        
    logging.info("Mongo Write for Historical Outages Complete!")


def run_historical_outages_query():
    historical_query = f"""
    SELECT *
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.es_raw_outage_window
    WHERE data_entry_date = (SELECT MAX(data_entry_date) FROM {DBS_CATALOG}.{DBS_SCHEMA}.es_raw_outage_window);
  """
    logging.info("Running query for historical outages...")
    try:
        df = run_dbs_query(cursor, historical_query)

        if df.empty:
            logging.info(
                "The Historical Outages DataFrame is empty or not found. Skipping historical processing!"
            )
        else:
            process_historical_outages(df)
    except Exception as e:
        logging.error(
            f"Error running new query on {DBS_CATALOG}.{DBS_SCHEMA}.es_raw_outage_window: {e}"
        )


def process_active_outages(df):
    logging.info("Creating ActiveOutageRecord objects in outage_records list...")
    outage_records = []

    try:
        restored_df = run_restored_outages_query()
    except Exception as e:
        logging.info(f"Was not able to assign a df to restored_df due to an error: {e}")

    restored_df["region"] = restored_df["region"].apply(convert_to_titlecase)
    df["region"] = df["region"].apply(convert_to_titlecase)

    # Merge the two DataFrames on the 'region' column
    merged_df = df.merge(
        restored_df[["region", "restored_customer_outages", "restored_incidents"]],
        on="region",
        how="left",
    )

    # Fill missing values in the 'restoredCustomerOutages' and 'restoredIncidents' columns with 0 if needed
    merged_df["restored_customer_outages"].fillna(0, inplace=True)
    merged_df["restored_incidents"].fillna(0, inplace=True)
    merged_df["potential_momentary_outages"].fillna(0, inplace=True)
    merged_df["potential_momentary_incidents"].fillna(0, inplace=True)

    for _, row in merged_df.iterrows():
        outage_records.append(
            ActiveOutageRecord(
                region=str(row["region"]),
                activeCustomerOutages=float(row["customer_outages"]),
                activeIncidents=int(row["incidents"]),
                restoredCustomerOutages=int(row["restored_customer_outages"]),
                restoredIncidents=int(row["restored_incidents"]),
                potentialMomentaryCustomerOutages=int(
                    row["potential_momentary_outages"]
                ),
                potentialMomentaryIncidents=int(row["potential_momentary_incidents"]),
            )
        )

    logging.info("Creating Writer Session...")
    session = ETRSessionBuilder(sessionType="active_outage").build()
    last_updated = df.iloc[0]["datetime_hour"]

    logging.info("Creating docs and writing to Mongo...")
    with session as s:
        s.add_document(
            ActiveOutagesDocumentBuilder(
                id=activeOutageId,
                session=s.id,
                outageRecords=outage_records,
                lastUpdated=last_updated,
                createdTimestamp=formatted_current_time,
                generatedBy="System",
            ).build()
        )

    logging.info("Mongo Write for Active Outages Complete!")
    run_historical_outages_query()


def run_active_outages_query():
    active_outages_query = f"""
    WITH MaxDateTimeHours AS (
        SELECT 
            region,
            MAX(datetime_hour) AS latest_datetime_hour
        FROM {DBS_CATALOG}.{DBS_SCHEMA}.es_aggregated_outages
        GROUP BY region
    )

    SELECT DISTINCT
        e.region,
        e.datetime_hour,
        e.incidents,
        e.customer_outages,
        e.potential_momentary_incidents,
        e.potential_momentary_outages
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.es_aggregated_outages e
    JOIN MaxDateTimeHours mdh 
    ON e.region = mdh.region AND e.datetime_hour = mdh.latest_datetime_hour;
    """  # noqa

    try:
        df = run_dbs_query(cursor, active_outages_query)

        if df.empty:
            logging.info(
                "The Active Outages DataFrame is empty or not found. Skipping processing and writing active outages!"
            )
            run_historical_outages_query()
        else:
            process_active_outages(df)

    except Exception as e:
        logging.error(f"Error running new query on es_aggregated_outages: {e}")


# restored outages
def run_restored_outages_query():
    restored_outages_query = f"""
    WITH MaxDateTimeHours AS (
        SELECT 
            region,
            MAX(restoration_time) AS latest_restoration_time
        FROM {DBS_CATALOG}.{DBS_SCHEMA}.es_aggregated_restored_outages
        GROUP BY region
    )

    SELECT DISTINCT
        e.region,
        e.restoration_time,
        e.restored_incidents,
        e.restored_customer_outages
    FROM {DBS_CATALOG}.{DBS_SCHEMA}.es_aggregated_restored_outages e
    JOIN MaxDateTimeHours mdh 
    ON e.region = mdh.region AND e.restoration_time = mdh.latest_restoration_time;
    """

    try:
        df = run_dbs_query(cursor, restored_outages_query)
        logging.info("df from query on es_aggregated_outages :")
        logging.info(df)

        if df.empty:
            logging.info(
                "The Restored Outages DataFrame is empty or not found. Skipping processing!"
            )
        else:
            return df

    except Exception as e:
        logging.error(
            f"Error running restored outages query on es_aggregated_restored_outages: {e}"
        )


def convert_mongo_dates(doc):
    """
    Recursively converts MongoDB Extended JSON date formats in a document
    to Python datetime objects. Modifies the document in-place.
    """
    for key, value in doc.items():
        if isinstance(value, dict):
            if "$date" in value:
                # MongoDB Extended JSON v2 date format
                date_value = value["$date"]
                if "$numberLong" in date_value:
                    # Convert milliseconds to seconds
                    timestamp = int(date_value["$numberLong"]) / 1000.0
                    doc[key] = datetime.fromtimestamp(timestamp)
                else:
                    doc[key] = datetime.fromisoformat(date_value)
            else:
                convert_mongo_dates(value)


def insert_documents_into_mongo(json_file: str, db_name: str, db_collection: str):
    logging.info(f"Analyzing the file {json_file}...")

    try:
        with open(json_file, "r") as file:
            try:
                documents = json.load(file)
            except json.JSONDecodeError as e:
                logging.error(f"Error reading {json_file}: {e}")
                return

        client = MongoClient(
            host=mongo_host,
            port=mongo_port,
            username=mongo_username,
            password=mongo_password,
            authSource="admin",
            authMechanism="SCRAM-SHA-256",
        )

        db = client[db_name]

        if not isinstance(documents, list):
            documents = [documents]

        if db_collection not in ["storm_component_config", "tileset"]:
            if db_collection in db.list_collection_names():
                logging.info(
                    f"Collection '{db_collection}' already exists in database '{db_name}'. Skipping insertion."
                )
                return
            else:
                collection = db[db_collection]

        for doc in documents:
            collection = db[db_collection]

            # Remove the "_id" field from the document if it exists
            doc.pop("_id", None)

            # Convert any MongoDB Extended JSON date formats to datetime objects
            convert_mongo_dates(doc)

            if db_collection in ["storm_component_config", "tileset"]:
                if "component" not in doc:
                    logging.warning(
                        f"Skipped a document without 'component' field: {doc}"
                    )
                    continue
                filter_criteria = {"component": doc["component"]}
                result = collection.update_one(
                    filter_criteria, {"$set": doc}, upsert=True
                )
                action = "Inserted" if result.upserted_id else "Updated"
                logging.info(f"{action} document with component: {doc['component']}")
            else:
                # New file will be inserted if the collection does not exist.
                result = collection.insert_one(doc)
                logging.info(f"Inserted new document: {result.inserted_id}")

    except Exception as e:
        logging.error(f"Error interacting with MongoDB: {str(e)}")
    finally:
        if "client" in locals():
            client.close()


def replace_mongo_collection(json_file: str, db_name: str, db_collection: str):
    logging.info(
        f"Analyzing {json_file} for replacing the {db_collection} collection..."
    )

    try:
        with open(json_file, "r") as file:
            try:
                documents = json.load(file)
            except json.JSONDecodeError as e:
                logging.error(f"Error reading {json_file}: {e}")
                return

        client = MongoClient(
            host=mongo_host,
            port=mongo_port,
            username=mongo_username,
            password=mongo_password,
            authSource="admin",
            authMechanism="SCRAM-SHA-256",
        )

        db = client[db_name]

        if not isinstance(documents, list):
            documents = [documents]

        if db_collection in db.list_collection_names():
            logging.info(
                f"Collection '{db_collection}' already exists in database '{db_name}'. Dropping and replacing."
            )
            db.drop_collection(db_collection)

        collection = db[db_collection]

        collection.insert_many(documents)
        logging.info(
            f"Successfully replaced the '{db_collection}' collection with new documents."
        )

    except Exception as e:
        logging.error(f"Error interacting with MongoDB: {str(e)}")
    finally:
        if "client" in locals():
            client.close()


# Sets archived storms less than 8hr to 'removed'
def set_lt_8hr_storms_to_removed():
    client = MongoClient(
        host=mongo_host,
        port=mongo_port,
        username=mongo_username,
        password=mongo_password,
        authSource="admin",
        authMechanism="SCRAM-SHA-256",
    )

    db = client["etr"]
    collection = db["storm_record"]

    filter_condition = {
        "$and": [
            {
                "$expr": {
                    "$lt": [
                        {
                            "$divide": [
                                {"$subtract": ["$stormEndDate", "$stormStartDate"]},
                                1000 * 60 * 60,  # Convert milliseconds to hours
                            ]
                        },
                        8,  # Duration less than 8 hours
                    ]
                }
            },
            {"stormMode": "archive"},  # Documents where stormMode is 'archive'
        ]
    }
    update_operation = {"$set": {"stormMode": "removed"}}

    update = collection.update_many(filter_condition, update_operation)
    logging.info(f"Documents modified: {update.modified_count}")

    client.close()


def process_forecasting_files(forecasting_folder: str):
    """
    Process all JSON files in 'json/forecasting', extracting db and collection names directly from file names.

    :param forecasting_folder: Relative path to the 'json/forecasting' folder.
    """
    for root, dirs, files in os.walk(forecasting_folder):
        for file in files:
            if file.endswith(".json"):
                # Extract database and collection names from filename
                parts = file.replace(".json", "").split("-")
                if len(parts) < 3:
                    logging.warning(f"Filename format not recognized: {file}")
                    continue

                db_name = parts[1]
                collection_name = "-".join(
                    parts[2:]
                )  # Keep underscores in collection names
                json_file_path = os.path.join(root, file)

                logging.info(
                    f"Processing {json_file_path} into DB: {db_name}, Collection: {collection_name}"
                )
                insert_documents_into_mongo(
                    json_file=json_file_path,
                    db_name=db_name,
                    db_collection=collection_name,
                )


def publish_event():
    client = RabbitMQClient()
    client.send_message(
        json.dumps(build_aggregate_overview_event()),
        routing_key=RABBITMQ_ROUTING_KEY,
        exchange=RABBITMQ_EXCHANGE,
    )
    client.close_connection()


def build_aggregate_overview_event():
    aggregate_overview_event = {
        "eventName": "aggregateOverviewEvent",
        "timestamp": to_rfc3339_string(current_time),  # pass in current time here
        "metadata": {
            "activeOutageId": str(activeOutageId),
            "unaggregatedOutageId": str(unaggregatedOutageId),
        },
    }

    return aggregate_overview_event


##################
### Main block ###
##################
if UPLOAD_CONFIGS:
    replace_mongo_collection(
        "json/explore/apc-explore-territory_types-full.json",
        "explore",
        "territory_types",
    )
    logging.info("territory_types collection replaced")
    replace_mongo_collection(
        "json/explore/apc-explore-territory-full.json", "explore", "territory"
    )
    logging.info("territory collection replaced")

    # insert_documents_into_mongo(
    #     "json/storm_component_config.json", "configurations", "storm_component_config"
    # )
    # logging.info("Updated storm_component_config files.")

    # insert_documents_into_mongo(
    #     "json/forecasting/apc-configurations-tileset.json", "configurations", "tileset"
    # )
    # logging.info("Updated tileset configs.")

    # # Insert files for forecasting/shifts
    # logging.info("About to process forecasting folder files.")
    # process_forecasting_files("json/forecasting")

# Call the function and handle errors gracefully
try:
    access_token = retrieve_access_token()
    logging.error("Access token retrieved successfully.")
except Exception as e:
    logging.error(f"An unexpected error occurred: {e}")
    sys.exit(1)

logging.info("Connecting to Databricks...")
conn = pyodbc.connect(dbs_conn_string(access_token), autocommit=True)
cursor = conn.cursor()
logging.info("Databricks connection complete...")

logging.info("Running Active Outages Query...")
run_active_outages_query()


############################
# Rabbit Publishing Message#
############################
logging.info("Running Aggregate Overview Metric Rabbit Publisher...")
try:
    publish_event()
except Exception as e:
    logging.error("Error: Running Aggregate Overview Metric Rabbit Publisher...", e)
    pass

############################
# Mongo Deleting #
############################
# Will delete historical_outages docs that have createdTimestamps over 2 weeks old and locked == False
logging.info("Deleting old outage docs..")
delete_2_week_unlocked_outages()
logging.info("2 week old outage docs with 'locked == False' have been deleted.")

logging.info("Setting storms less than 8 hr duration to 'removed'.")
set_lt_8hr_storms_to_removed()

logging.info("Script completed.")

# For working interactively in the pod for debugging
# def run():
#     while True:
#         try:
#             time.sleep(60)  # Simulate some work

#         except Exception as e:
#             logging.error(f"An error occurred: {e}")

# run()
